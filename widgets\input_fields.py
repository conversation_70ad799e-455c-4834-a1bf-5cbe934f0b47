import tkinter as tk

class InputFields(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.entries = {}
        self.default_qty = "75"

        fields = [
            ("Symbol", 16),
            ("Buy Price", 10),
            ("Buy Qty", 10),
            ("Sell Price", 10),
            ("Sell Qty", 10),
            ("Trigger Price", 10),
            ("User Stop Loss", 10)
        ]

        for label_text, entry_width in fields:
            tk.Label(self, text=label_text).pack(side="left")
            entry = tk.Entry(self, width=entry_width)
            entry.pack(side="left", padx=5)
            entry.config(state="disabled")
            self.entries[label_text] = entry

        # Set default quantity
        self.entries["Buy Qty"].insert(0, self.default_qty)
        self.entries["Sell Qty"].insert(0, self.default_qty)

        # Bind symbol entry to convert to uppercase
        self.entries["Symbol"].bind("<KeyRelease>", self._convert_symbol_to_upper)

    def _convert_symbol_to_upper(self, event):
        """Convert Symbol input to uppercase."""
        current_text = self.entries["Symbol"].get()
        self.entries["Symbol"].delete(0, tk.END)
        self.entries["Symbol"].insert(0, current_text.upper())

    def update_fields(self, trade_type):
        """Enable/disable fields based on trade type."""
        # Disable all fields first
        for entry in self.entries.values():
            entry.config(state="disabled")
            if entry.get() and entry != self.entries["Buy Qty"] and entry != self.entries["Sell Qty"]:
                entry.delete(0, tk.END)

        # Enable fields based on trade type
        self.entries["Symbol"].config(state="normal")
        self.entries["User Stop Loss"].config(state="normal")

        if trade_type == "Long_MKT":
            self.entries["Buy Qty"].config(state="normal")
            if not self.entries["Buy Qty"].get():
                self.entries["Buy Qty"].insert(0, self.default_qty)
        elif trade_type == "Short_MKT":
            self.entries["Sell Qty"].config(state="normal")
            if not self.entries["Sell Qty"].get():
                self.entries["Sell Qty"].insert(0, self.default_qty)
        elif trade_type in ["Long_LMT", "Long_SL"]:
            self.entries["Buy Price"].config(state="normal")
            self.entries["Buy Qty"].config(state="normal")
            if not self.entries["Buy Qty"].get():
                self.entries["Buy Qty"].insert(0, self.default_qty)
            if trade_type == "Long_SL":
                self.entries["Trigger Price"].config(state="normal")
                self._update_trigger_price("Long")
        elif trade_type in ["Short_LMT", "Short_SL"]:
            self.entries["Sell Price"].config(state="normal")
            self.entries["Sell Qty"].config(state="normal")
            if not self.entries["Sell Qty"].get():
                self.entries["Sell Qty"].insert(0, self.default_qty)
            if trade_type == "Short_SL":
                self.entries["Trigger Price"].config(state="normal")
                self._update_trigger_price("Short")
        elif trade_type in ["Long_SLM", "Short_SLM"]:
            qty_field = "Buy Qty" if trade_type == "Long_SLM" else "Sell Qty"
            self.entries[qty_field].config(state="normal")
            if not self.entries[qty_field].get():
                self.entries[qty_field].insert(0, self.default_qty)
            self.entries["Trigger Price"].config(state="normal")

    def _update_trigger_price(self, trade_direction):
        """Set default trigger price based on Buy/Sell price."""
        try:
            if trade_direction == "Long" and self.entries["Buy Price"].get():
                buy_price = float(self.entries["Buy Price"].get())
                trigger_price = buy_price - 0.50
                self.entries["Trigger Price"].delete(0, tk.END)
                self.entries["Trigger Price"].insert(0, f"{trigger_price:.2f}")
            elif trade_direction == "Short" and self.entries["Sell Price"].get():
                sell_price = float(self.entries["Sell Price"].get())
                trigger_price = sell_price + 0.50
                self.entries["Trigger Price"].delete(0, tk.END)
                self.entries["Trigger Price"].insert(0, f"{trigger_price:.2f}")
        except ValueError:
            pass

    def get_field_values(self):
        """Return current values of all input fields."""
        return {key: entry.get() for key, entry in self.entries.items()}