import tkinter as tk
import threading
import time
from shoonya_login import ShoonyaClient
from trade_monitor import TradeMonitor

class ActionButtons(tk.Frame):
    def __init__(self, master, trade_panel, input_fields):
        super().__init__(master)
        self.master = master
        self.trade_panel = trade_panel
        self.input_fields = input_fields
        self.client = ShoonyaClient()
        self.api = None
        self.is_buy_phase = True
        self.trade_monitor = None
        self.monitor_thread = None
        self.stop_monitoring = threading.Event()
        self.is_monitoring_active = False
        self.current_trade = {}  # Store trade details (symbol, qty, entry_price, trade_type, exchange)

        # Center buttons using a frame
        self.button_frame = tk.Frame(self)
        self.button_frame.pack(expand=True)

        self.status_label = tk.Label(self, text="", fg="green", font=("Arial", 10))
        self.status_label.pack(side="bottom", pady=5)

        self.login_button = tk.Button(self.button_frame, text="Login", command=self.login_action)
        self.login_button.pack(side="left", padx=10)

        self.exit_button = tk.Button(self.button_frame, text="Exit", command=self.exit_action)
        self.exit_button.pack(side="left", padx=10)

        self.buy_button = tk.Button(self.button_frame, text="Buy", command=self.buy_action)
        self.buy_button.pack(side="left", padx=10)
        self.buy_button.config(state="disabled")

        self.sell_button = tk.Button(self.button_frame, text="Sell", command=self.sell_action)
        self.sell_button.pack(side="left", padx=10)
        self.sell_button.config(state="disabled")

        self.sl_toggle_button = tk.Button(self.button_frame, text="Set SL", command=self.toggle_stop_loss, state="disabled")
        self.sl_toggle_button.pack(side="left", padx=10)

        self.summary_button = tk.Button(self.button_frame, text="Summary", command=lambda: self._show_temp_message("Summary not implemented", "blue"))
        self.summary_button.pack(side="left", padx=10)
        self.summary_button.config(state="disabled")

        # Add trace to trade_var to dynamically update input fields
        self.trade_panel.trade_var.trace_add("write", self._update_input_fields)

    def login_action(self):
        success = self.client.login()
        if success:
            self.api = self.client.api
            self._show_temp_message("Login successful ✅", "green")
            self.login_button.config(state="disabled")
            self.sell_button.config(state="disabled")
            self.buy_button.config(state="normal")
            self.sl_toggle_button.config(state="disabled")
            self.summary_button.config(state="normal")
            self.exit_button.config(state="normal")
            self.trade_panel.exchange_var.set("NSE")
            self.trade_panel.product_var.set("CNC")
            self.trade_panel.trade_var.set("Long_MKT")
            self.trade_panel.enable_trade_controls()
            self.update_button_states()
        else:
            self._show_temp_message("Login failed ❌", "red")

    def update_button_states(self):
        """Update Buy/Sell/SL button states and trade type radio buttons."""
        trade_type = self.trade_panel.trade_var.get()
        if self.is_buy_phase:
            if trade_type.startswith("Long"):
                self.buy_button.config(state="normal")
                self.sell_button.config(state="disabled")
                self.trade_panel.update_trade_radios()  # Enable all trade types in buy phase
            else:  # Short trade types
                self.buy_button.config(state="disabled")
                self.sell_button.config(state="normal")
                self.trade_panel.update_trade_radios()  # Enable all trade types in buy phase
            self.sl_toggle_button.config(state="disabled")
        else:
            current_direction = "Long" if self.current_trade.get("trade_type", "").startswith("Long") else "Short"
            self.buy_button.config(state="normal" if trade_type.startswith("Short") else "disabled")
            self.sell_button.config(state="normal" if trade_type.startswith("Long") else "disabled")
            self.sl_toggle_button.config(state="normal", text="Set SL" if not self.is_monitoring_active else "Cancel SL")
            self.trade_panel.update_trade_radios(current_direction)
        self._update_input_fields()

    def buy_action(self):
        if not self.api:
            self._show_temp_message("Not logged in", "red")
            return

        field_values = self.input_fields.get_field_values()
        trade_type = self.trade_panel.trade_var.get()
        product_type = self.trade_panel.product_var.get()
        exchange = self.trade_panel.exchange_var.get()

        order_params = self._prepare_order_params(field_values, trade_type, product_type, exchange, "B")
        if not order_params:
            self._show_temp_message("Invalid input values", "red")
            return

        try:
            response = self.api.place_order(**order_params)
            order_id = response.get("norenordno") if isinstance(response, dict) else None
            if order_id and self._is_order_placed(order_id, "B", order_params["tradingsymbol"]):
                self._show_temp_message("Buy order placed successfully ✅", "green")
                entry_price = float(field_values["Buy Price"] or "0") if trade_type in ["Long_LMT", "Long_SL"] else self._get_order_avg_price(order_id)
                self.current_trade = {
                    "symbol": order_params["tradingsymbol"],
                    "quantity": order_params["quantity"],
                    "entry_price": entry_price,
                    "trade_type": trade_type,
                    "exchange": exchange
                }
                print(f"[DEBUG] Buy order placed: {self.current_trade}")
                if trade_type.startswith("Long"):
                    self._transition_to_sell_phase()
                else:
                    self._transition_to_buy_phase()
            else:
                self._show_temp_message(f"Buy order failed: {response.get('rejreason', 'Unknown error')}", "red")
        except Exception as e:
            self._show_temp_message(f"Buy order error: {str(e)}", "red")
            print(f"[ERROR] Buy action failed: {str(e)}")

    def sell_action(self):
        if not self.api:
            self._show_temp_message("Not logged in", "red")
            return

        field_values = self.input_fields.get_field_values()
        trade_type = self.trade_panel.trade_var.get()
        product_type = self.trade_panel.product_var.get()
        exchange = self.trade_panel.exchange_var.get()

        order_params = self._prepare_order_params(field_values, trade_type, product_type, exchange, "S")
        if not order_params:
            self._show_temp_message("Invalid input values", "red")
            return

        try:
            response = self.api.place_order(**order_params)
            order_id = response.get("norenordno") if isinstance(response, dict) else None
            if order_id and self._is_order_placed(order_id, "S", order_params["tradingsymbol"]):
                self._show_temp_message("Sell order placed successfully ✅", "green")
                entry_price = float(field_values["Sell Price"] or "0") if trade_type in ["Short_LMT", "Short_SL"] else self._get_order_avg_price(order_id)
                self.current_trade = {
                    "symbol": order_params["tradingsymbol"],
                    "quantity": order_params["quantity"],
                    "entry_price": entry_price,
                    "trade_type": trade_type,
                    "exchange": exchange
                }
                print(f"[DEBUG] Sell order placed: {self.current_trade}")
                if trade_type.startswith("Short"):
                    self._transition_to_buy_phase()
                else:
                    self._reset_trade_phase()
            else:
                self._show_temp_message(f"Sell order failed: {response.get('rejreason', 'Unknown error')}", "red")
        except Exception as e:
            self._show_temp_message(f"Sell order error: {str(e)}", "red")
            print(f"[ERROR] Sell action failed: {str(e)}")

    def toggle_stop_loss(self):
        """Toggle stop-loss monitoring on/off."""
        if not self.is_monitoring_active:
            field_values = self.input_fields.get_field_values()
            try:
                stop_loss_price = float(field_values["User Stop Loss"]) if field_values["User Stop Loss"] else 0.0
                if stop_loss_price <= 0:
                    self._show_temp_message("Invalid User Stop Loss price", "red")
                    print("[DEBUG] Invalid User Stop Loss price")
                    return

                if not self.current_trade.get("symbol"):
                    self._show_temp_message("No active trade to monitor", "red")
                    print("[DEBUG] No active trade to monitor")
                    return

                trade_direction = "long" if self.current_trade["trade_type"].startswith("Long") else "short"
                self.trade_monitor = TradeMonitor(
                    self.api,
                    self.current_trade["exchange"],
                    self.current_trade["symbol"],
                    trade_direction,
                    self.current_trade["entry_price"],
                    stop_loss_price,
                    self.current_trade["quantity"],
                    self.input_fields
                )
                self.stop_monitoring.clear()
                self.monitor_thread = threading.Thread(target=self.trade_monitor.monitor, args=(self._on_stop_loss_triggered,))
                self.monitor_thread.daemon = True
                self.monitor_thread.start()
                self.is_monitoring_active = True
                self.sl_toggle_button.config(text="Cancel SL")
                self._show_temp_message("User Stop Loss set ✅", "green")
                print(f"[DEBUG] Stop-loss monitoring started: {self.current_trade}, SL: {stop_loss_price}")
            except (ValueError, KeyError) as e:
                self._show_temp_message(f"Error setting stop-loss: {str(e)}", "red")
                print(f"[ERROR] Toggle stop-loss failed: {str(e)}")
        else:
            print("[DEBUG] Attempting to cancel stop-loss")
            self.stop_monitoring.set()
            if self.monitor_thread:
                try:
                    self.monitor_thread.join(timeout=2.0)  # Timeout to prevent GUI freeze
                    if self.monitor_thread.is_alive():
                        print("[DEBUG] Monitor thread did not terminate within timeout")
                        self._show_temp_message("Warning: Stop-loss cancellation may be delayed", "orange")
                    else:
                        print("[DEBUG] Monitor thread terminated successfully")
                except Exception as e:
                    print(f"[ERROR] Error joining monitor thread: {str(e)}")
                    self._show_temp_message(f"Error canceling stop-loss: {str(e)}", "red")
            self.trade_monitor = None
            self.is_monitoring_active = False
            self.sl_toggle_button.config(text="Set SL")
            self.input_fields.entries["User Stop Loss"].config(state="normal")
            self.input_fields.entries["User Stop Loss"].delete(0, tk.END)
            self.input_fields.entries["User Stop Loss"].insert(0, "0")
            self._show_temp_message("User Stop Loss canceled ❌", "red")
            print("[DEBUG] Stop-loss canceled, User Stop Loss reset to 0")

    def _prepare_order_params(self, field_values, trade_type, product_type, exchange, buy_or_sell):
        """Prepare parameters for Shoonya API order placement with quantity validation."""
        try:
            symbol = field_values["Symbol"]
            if not symbol or not all(c.isalnum() or c in ['-', '_'] for c in symbol):
                print("[DEBUG] Invalid symbol")
                return None

            product = "C" if product_type == "CNC" else "I"
            price_type_map = {
                "Long_MKT": "MKT", "Short_MKT": "MKT",
                "Long_LMT": "LMT", "Short_LMT": "LMT",
                "Long_SL": "SL-LMT", "Short_SL": "SL-LMT",
                "Long_SLM": "SL-MKT", "Short_SLM": "SL-MKT"
            }
            price_type = price_type_map.get(trade_type)

            max_qty = self.current_trade.get("quantity", self.input_fields.default_qty)
            qty_field = "Buy Qty" if buy_or_sell == "B" else "Sell Qty"
            try:
                quantity = int(field_values[qty_field] or self.input_fields.default_qty)
                if quantity <= 0 or quantity > max_qty:
                    print(f"[DEBUG] Invalid {qty_field}: {quantity}, max allowed: {max_qty}")
                    self._show_temp_message(f"{qty_field} must be between 1 and {max_qty}", "red")
                    return None
            except ValueError:
                print(f"[DEBUG] Invalid {qty_field} value")
                return None

            price = float(field_values["Buy Price" if buy_or_sell == "B" else "Sell Price"] or "0")
            trigger_price = float(field_values["Trigger Price"] or "0") if trade_type in ["Long_SL", "Short_SL", "Long_SLM", "Short_SLM"] else None

            if trade_type in ["Long_LMT", "Long_SL"] and buy_or_sell == "B" and price <= 0:
                print("[DEBUG] Invalid buy price for LMT/SL")
                return None
            if trade_type in ["Short_LMT", "Short_SL"] and buy_or_sell == "S" and price <= 0:
                print("[DEBUG] Invalid sell price for LMT/SL")
                return None
            if trade_type in ["Long_SL", "Long_SLM", "Short_SL", "Short_SLM"] and trigger_price <= 0:
                print("[DEBUG] Invalid trigger price for SL/SLM")
                return None

            params = {
                "buy_or_sell": buy_or_sell,
                "product_type": product,
                "exchange": exchange,
                "tradingsymbol": symbol,
                "quantity": quantity,
                "discloseqty": 0,
                "price_type": price_type,
                "price": price,
                "trigger_price": trigger_price,
                "retention": "DAY",
                "remarks": "my_order_001"
            }
            print(f"[DEBUG] Order params: {params}")
            return params
        except (ValueError, KeyError) as e:
            print(f"[DEBUG] Error preparing order params: {str(e)}")
            return None

    def _is_order_placed(self, order_id, buy_or_sell, trading_symbol):
        """Check if the order is in the order book with status COMPLETE."""
        try:
            order_book = self.api.get_order_book()
            if not order_book or not isinstance(order_book, list):
                print("[DEBUG] Order book empty or invalid")
                return False
            for order in order_book:
                if (
                    order.get("norenordno") == order_id
                    and order.get("trantype") == buy_or_sell
                    and order.get("tsym") == trading_symbol
                    and order.get("status") == "COMPLETE"
                ):
                    print(f"[DEBUG] Order confirmed: {order_id}, {buy_or_sell}, {trading_symbol}")
                    return True
            print(f"[DEBUG] Order not found or not COMPLETE: {order_id}")
            return False
        except Exception as e:
            print(f"[ERROR] Failed to check order book: {str(e)}")
            return False

    def _get_order_avg_price(self, order_id):
        """Get the average price of the order from the order book."""
        try:
            order_book = self.api.get_order_book()
            if not order_book or not isinstance(order_book, list):
                print("[DEBUG] Order book empty for avg price")
                return 0.0
            for order in order_book:
                if order.get("norenordno") == order_id and order.get("status") == "COMPLETE":
                    avg_price = float(order.get("avgprc", "0"))
                    print(f"[DEBUG] Order {order_id} avg price: {avg_price}")
                    return avg_price
            print(f"[DEBUG] Order {order_id} not found for avg price")
            return 0.0
        except Exception as e:
            print(f"[ERROR] Failed to get order average price: {str(e)}")
            return 0.0

    def _on_stop_loss_triggered(self, trade_monitor):
        """Handle stop-loss trigger by placing a market order and logging the trade."""
        if trade_monitor.trade_closed:
            action = trade_monitor.action_taken.lower()
            order_params = {
                "buy_or_sell": action[0].upper(),
                "product_type": "C" if self.trade_panel.product_var.get() == "CNC" else "I",
                "exchange": trade_monitor.exchange,
                "tradingsymbol": trade_monitor.symbol,
                "quantity": int(trade_monitor.quantity),
                "discloseqty": 0,
                "price_type": "MKT",
                "price": 0,
                "trigger_price": None,
                "retention": "DAY",
                "remarks": "stop_loss_order"
            }
            print(f"[DEBUG] Placing stop-loss order: {order_params}")
            try:
                response = self.api.place_order(**order_params)
                order_id = response.get("norenordno") if isinstance(response, dict) else None
                if order_id and self._is_order_placed(order_id, action[0].upper(), trade_monitor.symbol):
                    self._show_temp_message(f"Stop-loss {action} order placed successfully ✅", "green")
                    trade_monitor.log_trade()
                    self._reset_trade_phase()
                    print("[DEBUG] Stop-loss order placed and trade reset")
                else:
                    self._show_temp_message(f"Stop-loss order failed: {response.get('rejreason', 'Unknown error')}", "red")
                    print(f"[DEBUG] Stop-loss order failed: {response}")
            except Exception as e:
                self._show_temp_message(f"Stop-loss order error: {str(e)}", "red")
                print(f"[ERROR] Stop-loss order error: {str(e)}")

    def _transition_to_sell_phase(self):
        """Transition to sell phase for Long trades: enable Sell, SL, Long trade types, and relevant fields."""
        self.is_buy_phase = False
        self.buy_button.config(state="disabled")
        self.sell_button.config(state="normal")
        self.sl_toggle_button.config(state="normal", text="Set SL")
        self.trade_panel.exchange_menu.config(state="disabled")
        self.trade_panel.product_radios[0].config(state="disabled")
        self.trade_panel.product_radios[1].config(state="disabled")
        self.trade_panel.update_trade_radios("Long")
        for entry in self.input_fields.entries.values():
            entry.config(state="disabled")
            entry.delete(0, tk.END)
        self.input_fields.entries["Symbol"].config(state="normal")
        self.input_fields.entries["Symbol"].insert(0, self.current_trade.get("symbol", ""))
        self.input_fields.entries["Sell Qty"].config(state="normal")
        self.input_fields.entries["Sell Qty"].insert(0, str(self.current_trade.get("quantity", self.input_fields.default_qty)))
        self.input_fields.entries["User Stop Loss"].config(state="normal")
        self.input_fields.entries["User Stop Loss"].insert(0, "0")
        self._update_input_fields()
        print("[DEBUG] Transitioned to sell phase")

    def _transition_to_buy_phase(self):
        """Transition to buy phase for Short trades: enable Buy, SL, Short trade types, and relevant fields."""
        self.is_buy_phase = False
        self.buy_button.config(state="normal")
        self.sell_button.config(state="disabled")
        self.sl_toggle_button.config(state="normal", text="Set SL")
        self.trade_panel.exchange_menu.config(state="disabled")
        self.trade_panel.product_radios[0].config(state="disabled")
        self.trade_panel.product_radios[1].config(state="disabled")
        self.trade_panel.update_trade_radios("Short")
        for entry in self.input_fields.entries.values():
            entry.config(state="disabled")
            entry.delete(0, tk.END)
        self.input_fields.entries["Symbol"].config(state="normal")
        self.input_fields.entries["Symbol"].insert(0, self.current_trade.get("symbol", ""))
        self.input_fields.entries["Buy Qty"].config(state="normal")
        self.input_fields.entries["Buy Qty"].insert(0, str(self.current_trade.get("quantity", self.input_fields.default_qty)))
        self.input_fields.entries["User Stop Loss"].config(state="normal")
        self.input_fields.entries["User Stop Loss"].insert(0, "0")
        self._update_input_fields()
        print("[DEBUG] Transitioned to buy phase")

    def _update_input_fields(self, *args):
        """Dynamically update input field states based on trade type."""
        if self.is_buy_phase:
            trade_type = self.trade_panel.trade_var.get()
            self.input_fields.entries["Buy Qty"].config(state="normal")
            self.input_fields.entries["Sell Qty"].config(state="disabled")
            self.input_fields.entries["Buy Price"].config(state="normal" if trade_type in ["Long_LMT", "Long_SL"] else "disabled")
            self.input_fields.entries["Sell Price"].config(state="disabled")
            self.input_fields.entries["Trigger Price"].config(state="normal" if trade_type in ["Long_SL", "Long_SLM"] else "disabled")
        else:
            trade_type = self.trade_panel.trade_var.get()
            trade_direction = "Long" if self.current_trade.get("trade_type", "").startswith("Long") else "Short"
            if trade_direction == "Long":
                self.input_fields.entries["Sell Qty"].config(state="normal")
                self.input_fields.entries["Buy Qty"].config(state="disabled")
                self.input_fields.entries["Sell Price"].config(state="normal" if trade_type in ["Long_LMT", "Long_SL"] else "disabled")
                self.input_fields.entries["Buy Price"].config(state="disabled")
                self.input_fields.entries["Trigger Price"].config(state="normal" if trade_type in ["Long_SL", "Long_SLM"] else "disabled")
            else:
                self.input_fields.entries["Buy Qty"].config(state="normal")
                self.input_fields.entries["Sell Qty"].config(state="disabled")
                self.input_fields.entries["Buy Price"].config(state="normal" if trade_type in ["Short_LMT", "Short_SL"] else "disabled")
                self.input_fields.entries["Sell Price"].config(state="disabled")
                self.input_fields.entries["Trigger Price"].config(state="normal" if trade_type in ["Short_SL", "Short_SLM"] else "disabled")
        print(f"[DEBUG] Updated input fields for trade type: {trade_type}")

    def _reset_trade_phase(self):
        """Reset to initial state for a new trade."""
        self.stop_monitoring.set()
        if self.monitor_thread:
            try:
                self.monitor_thread.join(timeout=2.0)
                if self.monitor_thread.is_alive():
                    print("[DEBUG] Reset: Monitor thread still alive after timeout")
                else:
                    print("[DEBUG] Reset: Monitor thread terminated")
            except Exception as e:
                print(f"[ERROR] Reset: Error joining monitor thread: {str(e)}")
        self.trade_monitor = None
        self.is_monitoring_active = False
        self.current_trade = {}
        self.is_buy_phase = True
        self.sell_button.config(state="disabled")
        self.sl_toggle_button.config(state="disabled", text="Set SL")
        self.buy_button.config(state="normal")
        self.trade_panel.exchange_var.set("NSE")
        self.trade_panel.product_var.set("CNC")
        self.trade_panel.trade_var.set("Long_MKT")
        self.trade_panel.enable_trade_controls()
        self.update_button_states()
        for entry in self.input_fields.entries.values():
            entry.config(state="normal")
            entry.delete(0, tk.END)
        self.input_fields.entries["Buy Qty"].insert(0, self.input_fields.default_qty)
        print("[DEBUG] Trade phase reset")

    def exit_action(self):
        countdown = [3, 2, 1]

        def tick():
            if countdown:
                seconds = countdown.pop(0)
                self.status_label.config(text=f"Closing app in {seconds} seconds...", fg="blue")
                self.master.after(1000, tick)
            else:
                self._cleanup_and_exit()

        tick()

    def _cleanup_and_exit(self):
        def background_cleanup():
            self.stop_monitoring.set()
            if self.monitor_thread:
                try:
                    self.monitor_thread.join(timeout=2.0)
                    if self.monitor_thread.is_alive():
                        print("[DEBUG] Exit: Monitor thread still alive after timeout")
                    else:
                        print("[DEBUG] Exit: Monitor thread terminated")
                except Exception as e:
                    print(f"[ERROR] Exit: Error joining monitor thread: {str(e)}")
            if self.api:
                try:
                    self.api.logout()
                except Exception as e:
                    print(f"[ERROR] Logout failed: {e}")
            self.master.quit()

        threading.Thread(target=background_cleanup, daemon=True).start()

    def _show_temp_message(self, message, color):
        self.status_label.config(text=message, fg=color)
        self.status_label.after(2000, lambda: self.status_label.config(text=""))
        print(f"[DEBUG] Message displayed: {message}")