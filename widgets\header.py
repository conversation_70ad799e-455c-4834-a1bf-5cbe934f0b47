import tkinter as tk
from utils.time_updater import update_time

class Header(tk.Frame):
    def __init__(self, master, client):
        super().__init__(master)
        self.client = client
        self.is_logged_in = False
        self.label = tk.Label(self, font=("Arial", 12))
        self.label.pack()
        self.mtm_label = tk.Label(self, text="MTM: 0.00", font=("Arial", 10))
        self.mtm_label.pack()
        update_time(self.label)

    def start_mtm_update(self):
        """Start periodic MTM updates after login."""
        if self.is_logged_in:
            self._update_mtm()
        self.after(5000, self.start_mtm_update)

    def _update_mtm(self):
        """Update MTM display."""
        if self.client and self.is_logged_in:
            mtm = self.client.get_total_mtm()
            self.mtm_label.config(text=f"MTM: {mtm:.2f}")

    def set_logged_in(self, logged_in):
        """Set login status to control MTM updates."""
        self.is_logged_in = logged_in
        if logged_in:
            self._update_mtm()