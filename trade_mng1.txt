(symbol in our input_fields.py file of our app) ts = 'TATAMOTORS-EQ' this is an example
(Exchange in dropdown box selected in our app) exg = 'NSE' this is an example
Buy price (price = bp) if price given when Long_LMT or Long_SL and can be edited while placing order
Sell price (price = sp) if price given when Short_LMT or Short_SL and can be edited while placing order
quanitity = 75 is by default can be edited by user while placing order

Long_MKT type all below for CNC and MIS product_types
Short_MKT type all below for CNC and MIS product_types

# Buy order to be placed on the NSE Market order, for CNC cash and carry
op_buyorder_mkt = api.place_order(buy_or_sell='B', product_type='C',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='MKT', price=0, trigger_price=None,
                        retention='DAY', remarks='my_order_001')


# Sell order to be placed on the NSE Market order, for CNC cash and carry
op_sellorder_mkt = api.place_order(buy_or_sell='S', product_type='C',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='MKT', price=0, trigger_price=None,
                        retention='DAY', remarks='my_order_001')


# Buy order to be placed on the NSE Market order, for MIS (intraday)
op_buyorder_mkt = api.place_order(buy_or_sell='B', product_type='I',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='MKT', price=0, trigger_price=None,
                        retention='DAY', remarks='my_order_001')


# Sell order to be placed on the NSE Market order, for MIS (intraday)
op_sellorder_mkt = api.place_order(buy_or_sell='S', product_type='I',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='MKT', price=0, trigger_price=None,
                        retention='DAY', remarks='my_order_001')


---------------------------------------------------------------------------------------------------

Long_LMT type all below for CNC and MIS product_types
Short_LMT type all below for CNC and MIS product_types

# Buy order to be placed on the NSE LIMIT order, for CNC cash and carry
op_buyorder_mkt = api.place_order(buy_or_sell='B', product_type='C',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='LMT', price=bp, trigger_price=None,
                        retention='DAY', remarks='my_order_001')


# Sell order  to be placed on the NSE LIMIT order, for CNC cash and carry
op_sellorder_mkt = api.place_order(buy_or_sell='S', product_type='C',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='LMT', price=sp, trigger_price=None,
                        retention='DAY', remarks='my_order_001')


# Buy order to be placed on the NSE LIMIT order, for MIS (intraday)
op_buyorder_mkt = api.place_order(buy_or_sell='B', product_type='I',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='LMT', price=bp, trigger_price=None,
                        retention='DAY', remarks='my_order_001')


# Sell order to be placed on the NSE LIMIT order, for MIS (intraday)
op_sellorder_mkt = api.place_order(buy_or_sell='S', product_type='I',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='LMT', price=sp, trigger_price=None,
                        retention='DAY', remarks='my_order_001')

---------------------------------------------------------------------------------------------------

Long_SL type all below for CNC and MIS product_types
Short_SL type all below for CNC and MIS product_types
trigger_price= tp (our app Trigger price as tp variable will be given by user during placing or orders)

# Buy order to be placed on the NSE SL-LMT order, for CNC cash and carry
op_buyorder_mkt = api.place_order(buy_or_sell='B', product_type='C',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='SL-LMT', price=bp, trigger_price=tp,
                        retention='DAY', remarks='my_order_001')


# Sell order  to be placed on the NSE SL-LMT order, for CNC cash and carry
op_sellorder_mkt = api.place_order(buy_or_sell='S', product_type='C',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='SL-LMT', price=sp, trigger_price=tp,
                        retention='DAY', remarks='my_order_001')


# Buy order to be placed on the NSE SL-LMT order, for MIS (intraday)
op_buyorder_mkt = api.place_order(buy_or_sell='B', product_type='I',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='SL-LMT', price=bp, trigger_price=tp,
                        retention='DAY', remarks='my_order_001')


# Sell order to be placed on the NSE SL-LMT order, for MIS (intraday)
op_sellorder_mkt = api.place_order(buy_or_sell='S', product_type='I',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='SL-LMT', price=sp, trigger_price=tp,
                        retention='DAY', remarks='my_order_001')

---------------------------------------------------------------------------------------------------

Long_SLM type all below for CNC and MIS product_types
Short_SLM type all below for CNC and MIS product_types
trigger_price= tp (our app Trigger price as tp variable will be given by user during placing or orders)

# Buy order to be placed on the NSE SLM order, for CNC cash and carry
op_buyorder_mkt = api.place_order(buy_or_sell='B', product_type='C',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='SL-MKT', price=0, trigger_price=tp,
                        retention='DAY', remarks='my_order_001')


# Sell order  to be placed on the NSE SLM order, for CNC cash and carry
op_sellorder_mkt = api.place_order(buy_or_sell='S', product_type='C',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='SL-MKT', price=0, trigger_price=tp,
                        retention='DAY', remarks='my_order_001')


# Buy order to be placed on the NSE SLM order, for MIS (intraday)
op_buyorder_mkt = api.place_order(buy_or_sell='B', product_type='I',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='SL-MKT', price=0, trigger_price=tp,
                        retention='DAY', remarks='my_order_001')


# Sell order to be placed on the NSE SLM order, for MIS (intraday)
op_sellorder_mkt = api.place_order(buy_or_sell='S', product_type='I',
                        exchange = exg, tradingsymbol=ts,
                        quantity=75, discloseqty=0,price_type='SL-MKT', price=0, trigger_price=tp,
                        retention='DAY', remarks='my_order_001')

above all are functionalites required when below combinations and conditions met then above buy any one order will be used by Buy button and above any one sell order will be used by Sell button when clicked by user after setting below radio buttons and then those enabled input fields of relevant setting of Long and Short types once filled by user then buy or sell order will be placed once placed we will receive a confirmation from api call response our code will catch that and display the confirmation that order placed successfully then only next move will taken.  
If user selects after the login successful any product radio button either CNC or MIS, and in
Long_MKT or Long_LMT or Long_SL or Long_SLM or Short_MKT or Short_LMT or Short_SL or Short_SLM, these are the below scenarios:
example1:
user selected radio buttons like
CNC selected with mouse or keyboard, then Long_MKT selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, long trade , so enable Buy Qty input field default here 75, can be edited by user if he wants, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like below or different:
[{'stat': 'Ok', 'norenordno': '25080700390878', 'kidid': '1', 'uid': 'FA431915', 'actid': 'FA431915', 'exch': 'NSE', 'tsym': 'TATAMOTORS-EQ', 'rejby': 'RED', 'src_uid': '', 'qty': '25', 'ipaddr': '************', 'ordenttm': '1754553676', 'trantype': 'B', 'prctyp': 'MKT', 'ret': 'DAY', 'rejreason': 'RED:Margin Shortfall:INR 1,346.99 Available:INR 2,002.25 for C-FA431915 [SHOONYA]', 'token': '3456', 'mult': '1', 'prcftr': '1.000000', 'instname': 'EQ', 'ordersource': 'API', 'pp': '2', 'ls': '1', 'ti': '0.05', 'prc': '0.00', 'rprc': '0.00', 'dscqty': '0', 'brnchid': 'HO', 'C': 'C', 's_prdt_ali': 'MIS', 'prd': 'I', 'status': 'REJECTED', 'st_intrn': 'REJECTED', 'norentm': '13:31:16 07-08-2025', 'remarks': 'my_order_001', 'rqty': '0'}, {'stat': 'Ok', 'norenordno': '25080700141619', 'kidid': '2', 'uid': 'FA431915', 'actid': 'FA431915', 'exch': 'NFO', 'tsym': 'NIFTY07AUG25P24400', 'rejby': '', 'src_uid': 'FA431915', 'qty': '75', 'rorgqty': '75', 'ipaddr': '*************', 'ordenttm': '1754542736', 'mkt_protection': '5.00', 'trantype': 'S', 'prctyp': 'LMT', 'ret': 'DAY', 'rejreason': ' ', 'token': '39805', 'mult': '1', 'prcftr': '1.000000', 'instname': 'OPTIDX', 'ordersource': 'WEB', 'dname': 'NIFTY 07AUG25 24400 PE ', 'pp': '2', 'ls': '75', 'ti': '0.05', 'prc': '15.25', 'rorgprc': '21.00', 'rprc': '15.25', 'avgprc': '16.10', 'dscqty': '0', 'brnchid': 'HO', 'C': 'C', 's_prdt_ali': 'NRML', 'prd': 'M', 'status': 'COMPLETE', 'st_intrn': 'COMPLETE', 'fillshares': '75', 'norentm': '10:28:56 07-08-2025', 'exch_tm': '07-08-2025 10:28:56', 'exchordid': '1500000040644680', 'rqty': '75'}, {'stat': 'Ok', 'norenordno': '25080700132788', 'kidid': '1', 'uid': 'FA431915', 'actid': 'FA431915', 'exch': 'NFO', 'tsym': 'NIFTY07AUG25P24400', 'rejby': '', 'src_uid': '', 'qty': '75', 'ipaddr': '*************', 'ordenttm': '1754541583', 'mkt_protection': '5.00', 'trantype': 'B', 'prctyp': 'LMT', 'ret': 'DAY', 'rejreason': ' ', 'token': '39805', 'mult': '1', 'prcftr': '1.000000', 'instname': 'OPTIDX', 'ordersource': 'WEB', 'dname': 'NIFTY 07AUG25 24400 PE ', 'pp': '2', 'ls': '75', 'ti': '0.05', 'prc': '15.80', 'rprc': '15.80', 'avgprc': '14.90', 'dscqty': '0', 'brnchid': 'HO', 'C': 'C', 's_prdt_ali': 'NRML', 'prd': 'M', 'status': 'COMPLETE', 'st_intrn': 'COMPLETE', 'fillshares': '75', 'norentm': '10:09:43 07-08-2025', 'exch_tm': '07-08-2025 10:09:43', 'exchordid': '1500000037253316', 'rqty': '75'}]
in that take relevant to get confirmation that order is filled, just display the message and vanish after 2 seconds maximum
next disable Buy button and its relevant previous input fields and selected Trade type and Product, enable Sell button and its relevant input fields, let the user fill all the relevant fields then if he placed the sell order or Clicked the Sell button then wait for order filled or placed successfully confirmed and then only one trade Long_MKT completed, this complete trade transaction will be recorder in journal, this functionality i will give later on
next another selection for another trade like above
or
example2:
user selected radio buttons like
CNC selected with mouse or keyboard, then Short_MKT selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, short trade , so enable Sell Qty input field default here 75, can be edited by user if he wants, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

or
example3:
user selected radio buttons like
MIS selected with mouse or keyboard, then Short_MKT selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, short trade , so enable Sell Qty input field default here 75, can be edited by user if he wants, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

or
example4:
user selected radio buttons like
MIS selected with mouse or keyboard, then Long_MKT selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, long trade , so enable Buy Qty input field default here 75, can be edited by user if he wants, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

or
----------------------------------------------------------------------------------------------------------------
example1:
user selected radio buttons like
CNC selected with mouse or keyboard, then Long_LMT selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, long trade , so enable Buy price, input field, can be editable,  Buy Qty input field default here 75, can be edited by user if he wants, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

in that take relevant to get confirmation that order is filled, just display the message and vanish after 2 seconds maximum

next disable Buy button and its relevant previous input fields and selected Trade type and Product, enable Sell button and its relevant input fields, let the user fill all the relevant fields then if he placed the sell order or Clicked the Sell button then wait for order filled or placed successfully confirmed and then only one trade Long_LMT completed, this complete trade transaction will be recorder in journal, this functionality i will give later on
next another selection for another trade like above
or
example2:
user selected radio buttons like
CNC selected with mouse or keyboard, then Short_LMT selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, short trade , so enable Sell price, input field, can be editable, Sell Qty input field default here 75, can be edited by user if he wants, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

or

example3:
user selected radio buttons like
MIS selected with mouse or keyboard, then Long_LMT selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, long trade , so enable Buy price, input field, can be editable,  Buy Qty input field default here 75, can be edited by user if he wants, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

in that take relevant to get confirmation that order is filled, just display the message and vanish after 2 seconds maximum

next disable Buy button and its relevant previous input fields and selected Trade type and Product, enable Sell button and its relevant input fields, let the user fill all the relevant fields then if he placed the sell order or Clicked the Sell button then wait for order filled or placed successfully confirmed and then only one trade Long_LMT completed, this complete trade transaction will be recorder in journal, this functionality i will give later on
next another selection for another trade like above
or
example4:
user selected radio buttons like
MIS selected with mouse or keyboard, then Short_LMT selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, short trade , so enable Sell price, input field, can be editable, Sell Qty input field default here 75, can be edited by user if he wants, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

or
-----------------------------------------------------------------------------------------------------------------------
example1:
user selected radio buttons like
CNC selected with mouse or keyboard, then Long_SL selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, long trade , so enable Buy price, input field, can be editable,  Buy Qty input field default here 75, can be edited by user if he wants, next enable Trigger_price will be given by user, by default this price will be 0.50 less than the Buy price, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

in that take relevant to get confirmation that order is filled, just display the message and vanish after 2 seconds maximum

next disable Buy button and its relevant previous input fields and selected Trade type and Product, enable Sell button and its relevant input fields, let the user fill all the relevant fields then if he placed the sell order or Clicked the Sell button then wait for order filled or placed successfully confirmed and then only one trade Long_SL completed, this complete trade transaction will be recorder in journal, this functionality i will give later on
next another selection for another trade like above
or
example2:
user selected radio buttons like
CNC selected with mouse or keyboard, then Short_SL selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, short trade , so enable Sell price, input field, can be editable, Sell Qty input field default here 75, can be edited by user if he wants, next enable Trigger_price will be given by user, by default this price will be 0.50 more than the Sell price, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

or

example3:
user selected radio buttons like
MIS selected with mouse or keyboard, then Long_SL selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, long trade , so enable Buy price, input field, can be editable,  Buy Qty input field default here 75, can be edited by user if he wants, next enable Trigger_price will be given by user, by default this price will be 0.50 less than the Buy price, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

in that take relevant to get confirmation that order is filled, just display the message and vanish after 2 seconds maximum

next disable Buy button and its relevant previous input fields and selected Trade type and Product, enable Sell button and its relevant input fields, let the user fill all the relevant fields then if he placed the sell order or Clicked the Sell button then wait for order filled or placed successfully confirmed and then only one trade Long_SL completed, this complete trade transaction will be recorder in journal, this functionality i will give later on
next another selection for another trade like above
or
example4:
user selected radio buttons like
MIS selected with mouse or keyboard, then Short_SL selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, short trade , so enable Sell price, input field, can be editable, Sell Qty input field default here 75, can be edited by user if he wants, next enable Trigger_price will be given by user, by default this price will be 0.50 more than the Sell price, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

or
-----------------------------------------------------------------------------------------------------------------------
example1:
user selected radio buttons like
CNC selected with mouse or keyboard, then Long_SLM selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, long trade , Buy price will be 0, so enable Buy Qty input field default here 75, can be edited by user if he wants, next enable Trigger_price will be given by user, by default this price will be 0.50 less than the Buy price, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

in that take relevant to get confirmation that order is filled, just display the message and vanish after 2 seconds maximum

next disable Buy button and its relevant previous input fields and selected Trade type and Product, enable Sell button and its relevant input fields, let the user fill all the relevant fields then if he placed the sell order or Clicked the Sell button then wait for order filled or placed successfully confirmed and then only one trade Long_SLM completed, this complete trade transaction will be recorder in journal, this functionality i will give later on
next another selection for another trade like above
or
example2:
user selected radio buttons like
CNC selected with mouse or keyboard, then Short_SLM selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, short trade , Sell price will be 0, so enable Sell Qty input field default here 75, can be edited by user if he wants, next enable Trigger_price will be given by user, by default this price will be 0.50 more than the Sell price, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

or

example3:
user selected radio buttons like
MIS selected with mouse or keyboard, then Long_SLM selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, long trade , Buy price will be 0, so enable Buy Qty input field default here 75, can be edited by user if he wants, next enable Trigger_price will be given by user, by default this price will be 0.50 less than the Buy price, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.

in that take relevant to get confirmation that order is filled, just display the message and vanish after 2 seconds maximum

next disable Buy button and its relevant previous input fields and selected Trade type and Product, enable Sell button and its relevant input fields, let the user fill all the relevant fields then if he placed the sell order or Clicked the Sell button then wait for order filled or placed successfully confirmed and then only one trade Long_SLM completed, this complete trade transaction will be recorder in journal, this functionality i will give later on
next another selection for another trade like above
or
example4:
user selected radio buttons like
MIS selected with mouse or keyboard, then Short_SLM selected then enable the Symbol input field to enter a shoonya broker trading symbol to trade on, short trade , Sell price will be 0, so enable Sell Qty input field default here 75, can be edited by user if he wants, next enable Trigger_price will be given by user, by default this price will be 0.50 more than the Sell price, next enable User Stop Loss for this input field i will give some other time code what to do, for this time don't give any code to it just enable it enough,
next then enable Buy button, once Buy button clicked by user then order will be placed through api then we get response like previously given in above.
