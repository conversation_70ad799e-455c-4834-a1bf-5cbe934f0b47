import tkinter as tk

class TradePanel(tk.Frame):
    def __init__(self, master, input_fields, action_buttons=None):
        super().__init__(master)
        self.input_fields = input_fields
        self.action_buttons = action_buttons
        self.exchange_var = tk.StringVar(value="NSE")
        self.product_var = tk.StringVar(value="CNC")
        self.trade_var = tk.StringVar(value="Long_MKT")
        
        # Exchange dropdown
        self.exchange_label = tk.Label(self, text="Exchange")
        self.exchange_label.pack(side="left")
        self.exchange_menu = tk.OptionMenu(self, self.exchange_var, "NSE", "NFO", "MCX", "BSE", "BFO")
        self.exchange_menu.pack(side="left")
        self.exchange_menu.config(state="disabled")

        # Product type radio buttons (CNC/MIS)
        tk.Label(self, text="Product").pack(side="left", padx=5)
        self.product_radios = []
        for product in ["CNC", "MIS"]:
            rb = tk.Radiobutton(self, text=product, value=product, variable=self.product_var,
                                command=self._update_fields)
            rb.pack(side="left", padx=2)
            rb.config(state="disabled")
            self.product_radios.append(rb)

        tk.Label(self, text="   ").pack(side="left")

        # Trade types
        long_types = ["Long_MKT", "Long_LMT", "Long_SL", "Long_SLM"]
        short_types = ["Short_MKT", "Short_LMT", "Short_SL", "Short_SLM"]
        
        self.trade_radios = []
        for lt in long_types:
            rb = tk.Radiobutton(self, text=lt, value=lt, variable=self.trade_var,
                                command=self._update_fields)
            rb.pack(side="left", padx=2)
            rb.config(state="disabled")
            self.trade_radios.append(rb)

        tk.Label(self, text="   ").pack(side="left")

        for st in short_types:
            rb = tk.Radiobutton(self, text=st, value=st, variable=self.trade_var,
                                command=self._update_fields)
            rb.pack(side="left", padx=2)
            rb.config(state="disabled")
            self.trade_radios.append(rb)

    def enable_trade_controls(self):
        """Enable the Exchange dropdown, product radio buttons, and trade radio buttons."""
        self.exchange_menu.config(state="normal")
        for rb in self.product_radios:
            rb.config(state="normal")
        self.update_trade_radios()  # Enable all trade types initially
        self._update_fields()

    def update_trade_radios(self, trade_direction=None):
        """Update trade radio buttons based on current trade direction."""
        if trade_direction is None:
            # Enable all trade types
            for rb in self.trade_radios:
                rb.config(state="normal")
        else:
            # Enable only the specified direction (Long/Short)
            for rb in self.trade_radios:
                if rb["text"].startswith(trade_direction):
                    rb.config(state="normal")
                else:
                    rb.config(state="disabled")

    def _update_fields(self):
        """Update input fields and button states based on selected trade type."""
        self.input_fields.update_fields(self.trade_var.get())
        if self.action_buttons:
            self.action_buttons.update_button_states()