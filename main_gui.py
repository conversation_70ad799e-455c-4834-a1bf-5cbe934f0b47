import tkinter as tk
from widgets.header import Header
from widgets.status_bar import <PERSON><PERSON><PERSON>
from widgets.trade_panel import TradePanel
from widgets.input_fields import InputFields
from widgets.action_buttons import ActionButtons

class MainGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BMB Trading Dashboard")

        # Set window to stay on top
        self.root.attributes("-topmost", True)

        # Temporarily set geometry to allow widget packing
        self.root.geometry("1200x150+0+0")

        # Load widgets
        self.header = Header(self.root, None)
        self.header.pack(fill="x")
        self.status_bar = StatusBar(self.root)
        self.status_bar.pack(fill="x")
        self.trade_panel = TradePanel(self.root, None)
        self.trade_panel.pack(fill="x")
        self.input_fields = InputFields(self.root)
        self.input_fields.pack(fill="x")
        self.trade_panel.input_fields = self.input_fields
        self.action_buttons = ActionButtons(self.root, self.trade_panel, self.input_fields)
        self.action_buttons.pack(fill="x")
        self.header.client = self.action_buttons.client
        # Start MTM updates after ActionButtons is initialized
        self.action_buttons.login_button.config(command=lambda: self._login_action_wrapper())

        # Update layout to get actual window width
        self.root.update_idletasks()

        # Get actual window width after packing
        window_width = self.root.winfo_width()

        # Get screen width
        screen_width = self.root.winfo_screenwidth()

        # Desired vertical position
        y = 600

        # Calculate horizontal center
        x = (screen_width // 2) - (window_width // 2)

        # Apply final geometry
        self.root.geometry(f"{window_width}x{self.root.winfo_height()}+{x}+{y}")

    def _login_action_wrapper(self):
        """Wrap login action to set header login status."""
        self.action_buttons.login_action()
        if self.action_buttons.api:
            self.header.set_logged_in(True)

    def run(self):
        self.root.mainloop()