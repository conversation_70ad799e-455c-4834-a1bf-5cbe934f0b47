import tkinter as tk
from main_gui import MainGUI

class PasswordWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Enter Password")

        # Desired window size
        width, height = 300, 100

        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Calculate position
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)

        # Set geometry with position
        self.root.geometry(f"{width}x{height}+{x}+{y}")

        # Entry and button
        self.entry = tk.Entry(self.root, show="*")
        self.entry.focus()
        self.entry.pack(pady=10)

        self.button = tk.Button(self.root, text="Accept", command=self.check_password)
        self.button.pack()

    def check_password(self):
        if self.entry.get() == "0000":
            self.root.destroy()
            MainGUI().run()

    def run(self):
        self.root.mainloop()