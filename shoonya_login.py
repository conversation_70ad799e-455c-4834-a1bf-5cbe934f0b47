from __future__ import annotations

import json
import os
from datetime import datetime
from typing import Optional

import pandas as pd
from pyotp import TOTP
from NorenRestApiPy.NorenApi import NorenApi

class ShoonyaClient:
    REST_URL = "https://api.shoonya.com/NorenWClientTP/"
    WS_URL = "wss://api.shoonya.com/NorenWSTP/"

    def __init__(self, cred_file: str = "Cread.json", token_file: str = "session_token.txt"):
        self.cred_file = cred_file
        self.token_file = token_file
        self._api = None
        self._creds = None
        self._logged_in = False

    def login(self) -> bool:
        if self._logged_in:
            return True
        self._load_credentials()
        self._instantiate_api()
        otp = TOTP(self._creds["totp"]).now().zfill(6)
        response = self._api.login(
            userid=self._creds["user_id"],
            password=self._creds["password"],
            twoFA=otp,
            vendor_code=self._creds["vendor_code"],
            api_secret=self._creds["app_key"],
            imei=self._creds["imei"],
        )
        if response is None or "susertoken" not in response:
            return False
        self._save_token(response["susertoken"])
        self._logged_in = True
        return True

    def logout(self):
        if self._api and self._logged_in:
            self._api.logout()
            self._logged_in = False

    @property
    def api(self) -> Optional[NorenApi]:
        return self._api

    def _load_credentials(self) -> None:
        try:
            with open(self.cred_file, "r") as fp:
                self._creds = json.load(fp)
        except (FileNotFoundError, json.JSONDecodeError, KeyError) as exc:
            raise RuntimeError(f"Credential file error: {exc}") from None

    def _instantiate_api(self) -> None:
        class _Api(NorenApi):
            def __init__(self_):
                super().__init__(host=self.REST_URL, websocket=self.WS_URL)
        self._api = _Api()

    def _save_token(self, token: str) -> None:
        try:
            with open(self.token_file, "w") as fp:
                fp.write(token)
        except OSError:
            pass

    def get_total_mtm(self):
        try:
            if not self._api or not self._logged_in:
                return 0.0
            positions = self._api.get_positions()
            if not positions or not isinstance(positions, list):
                return 0.0
            mtm = 0.0
            pnl = 0.0
            for position in positions:
                if position.get('stat') == 'Ok':
                    urmtom = position.get('urmtom', '0.00')
                    rpnl = position.get('rpnl', '0.00')
                    try:
                        mtm += float(urmtom)
                        pnl += float(rpnl)
                    except (ValueError, TypeError):
                        pass
            return mtm + pnl
        except Exception:
            return 0.0