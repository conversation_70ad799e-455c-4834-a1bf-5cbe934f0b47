there are many issues in ur code
1. by default when login is successful message came there are radio buttons selected those are Exchange set as NSE, CNC and Long_MKT for this Symbol, Buy Qty and User Stop Loss input fields enabled, Login button and Sell button disabled ok fine, Exit, Buy and Summary are enabled.
but when user given input symbol say tcs-eq, ur code must convert this symbol into Capital letters, like TCS-EQ okay, next
2. Make this BMB Trading Dashboard or app stay visible all the time stick there itself even when user is cascading windows it must stick there, unless other wise he may close or click exit graciously it will close all, and user will only minimize or maximise he wants, till that time it must stay there only okay.
3. during 1 issue above when user placed buy order or clicked buy button if we get confirmation of order placed successfully then diable the buy button its previous input fields given even symbol also, and then enable sell button to place sell order.
4. when user selected Long_SLM any product CNC or MIS not an issue, here Buy Qty is ok, and enable Trigger Price okay user will input okay and for Short_SLM any product CNC or MIS not an issue, here Sell Qty is ok, and enable Trigger Price okay user will input okay.
5. GET ME mtm to display below the date time I will give some code u can use it:
def get_total_mtm(self):
        try:
            if not self.client or not self.client.api:
                print("MTM Error: API not initialized")
                return 0.0
            positions = self.client.api.get_positions()
            print(f"Positions response: {positions}")  # Debug logging
            if not positions or not isinstance(positions, list):
                print("MTM Warning: No positions or invalid response")
                return 0.0
            mtm = 0.0
            pnl = 0.0
            for position in positions:
                if position.get('stat') == 'Ok':
                    urmtom = position.get('urmtom', '0.00')
                    rpnl = position.get('rpnl', '0.00')
                    try:
                        mtm += float(urmtom)
                        pnl += float(rpnl)
                    except (ValueError, TypeError) as e:
                        print(f"MTM Error: Invalid urmtom/rpnl for position {position}: {e}")
            day_m2m = mtm + pnl
            return day_m2m
        except Exception as e:
            print(f"Error fetching positions for MTM: {e}")
            return 0.0
